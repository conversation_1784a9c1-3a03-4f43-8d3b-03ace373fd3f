use coco_server::database::{DatabaseConfig, SurrealDBClient};
use coco_server::database::migration::MigrationManager;
use coco_server::repositories::token_repository::TokenRepository;
use coco_server::services::token_service::TokenService;
use coco_server::models::access_token::AccessToken;
use std::sync::Arc;

/// 测试SurrealDB集成
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_surrealdb_integration() {
    // 初始化数据库客户端
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());

    // 执行数据库迁移
    let migration_manager = MigrationManager::new(db_client.clone());
    migration_manager.migrate().await.unwrap();

    // 创建TokenRepository和TokenService
    let token_repository = Arc::new(TokenRepository::new(db_client.clone()));
    let token_service = TokenService::new(token_repository.clone());

    // 测试生成令牌
    let user_id = "test-user";
    let token_name = Some("test-token".to_string());
    
    let token = token_service
        .generate_api_token(user_id, token_name)
        .await
        .unwrap();
    
    assert_eq!(token.user_id, user_id);
    assert_eq!(token.name, "test-token");
    assert!(token.is_active);

    // 测试验证令牌
    let validated_token = token_service
        .validate_api_token(&token.access_token)
        .await
        .unwrap();
    
    assert_eq!(validated_token.id, token.id);
    assert_eq!(validated_token.user_id, user_id);

    // 测试获取用户令牌
    let user_tokens = token_service
        .get_user_tokens(user_id)
        .await
        .unwrap();
    
    assert!(!user_tokens.is_empty());
    assert_eq!(user_tokens[0].id, token.id);

    // 测试重命名令牌
    let new_name = "renamed-token".to_string();
    token_service
        .rename_token(&token.id, user_id, new_name.clone())
        .await
        .unwrap();

    // 验证重命名成功
    let updated_tokens = token_service
        .get_user_tokens(user_id)
        .await
        .unwrap();
    
    assert_eq!(updated_tokens[0].name, new_name);

    // 测试撤销令牌
    token_service
        .revoke_token(&token.id, user_id)
        .await
        .unwrap();

    // 验证令牌已被撤销（应该无法验证）
    let result = token_service
        .validate_api_token(&token.access_token)
        .await;
    
    assert!(result.is_err());

    println!("✅ SurrealDB集成测试通过！");
}

/// 测试数据库迁移
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_database_migration() {
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());

    let migration_manager = MigrationManager::new(db_client.clone());
    
    // 执行迁移
    let result = migration_manager.migrate().await;
    assert!(result.is_ok());

    // 获取迁移状态
    let migration_status = migration_manager.get_migration_status().await.unwrap();
    assert!(!migration_status.is_empty());

    // 验证认证表已创建
    let query_result = db_client
        .db()
        .query("SELECT * FROM users LIMIT 1")
        .await;
    assert!(query_result.is_ok());

    let query_result = db_client
        .db()
        .query("SELECT * FROM access_tokens LIMIT 1")
        .await;
    assert!(query_result.is_ok());

    println!("✅ 数据库迁移测试通过！");
}

/// 测试TokenRepository的CRUD操作
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_token_repository_crud() {
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());

    // 执行迁移
    let migration_manager = MigrationManager::new(db_client.clone());
    migration_manager.migrate().await.unwrap();

    let repository = TokenRepository::new(db_client);

    // 创建测试令牌
    let token = AccessToken::new("test-user".to_string(), "test-token".to_string());
    let original_token = token.clone();

    // 测试创建
    let created_token = repository.create_token(&token).await.unwrap();
    assert_eq!(created_token.id, token.id);

    // 测试根据令牌字符串查找
    let found_token = repository
        .find_by_token(&token.access_token)
        .await
        .unwrap();
    assert!(found_token.is_some());
    assert_eq!(found_token.unwrap().id, token.id);

    // 测试根据ID查找
    let found_by_id = repository
        .find_by_id(&token.id)
        .await
        .unwrap();
    assert!(found_by_id.is_some());
    assert_eq!(found_by_id.unwrap().id, token.id);

    // 测试根据用户ID查找
    let user_tokens = repository
        .find_by_user_id("test-user")
        .await
        .unwrap();
    assert!(!user_tokens.is_empty());

    // 测试更新最后使用时间
    repository
        .update_last_used(&token.access_token)
        .await
        .unwrap();

    // 测试重命名
    repository
        .rename_token(&token.id, "new-name")
        .await
        .unwrap();

    // 测试撤销
    repository
        .revoke_token(&token.id)
        .await
        .unwrap();

    // 验证撤销后无法找到活跃令牌
    let revoked_token = repository
        .find_by_token(&original_token.access_token)
        .await
        .unwrap();
    assert!(revoked_token.is_none()); // 因为查询条件包含 is_active = true

    println!("✅ TokenRepository CRUD测试通过！");
}
