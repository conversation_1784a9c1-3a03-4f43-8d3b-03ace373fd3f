/// 集成测试：POST /auth/request_access_token 端点
///
/// 这个测试验证TASK-008的完整功能：
/// 1. API端点功能正常
/// 2. 令牌生成和存储正常
/// 3. 响应格式与规格一致
/// 4. 集成测试通过
use axum::{
    body::{Body, HttpBody},
    http::{Request, StatusCode},
    routing::post,
    Router,
};
use serde_json::json;
use std::sync::Arc;
use tower::ServiceExt;

use coco_server::app_state::AppState;
use coco_server::auth::token_blacklist::TokenBlacklist;
use coco_server::auth::user_claims::UserClaims;
use coco_server::database::{DatabaseConfig, SurrealDBClient};
use coco_server::handlers::token_handler::request_access_token_handler;
use coco_server::middleware::auth_middleware::auth_middleware;
use coco_server::models::access_token::{CreateTokenRequest, CreateTokenResponse};
use coco_server::repositories::token_repository::TokenRepository;
use coco_server::services::token_service::TokenService;

/// 创建测试用的AppState
async fn create_test_app_state() -> AppState {
    // 创建数据库客户端
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(
        SurrealDBClient::new(db_config)
            .await
            .expect("Failed to create test database client"),
    );

    // 创建TokenRepository
    let token_repository = Arc::new(TokenRepository::new(db_client.clone()));

    // 创建TokenService
    let token_service = Arc::new(TokenService::new(token_repository.clone()));

    // 创建TokenBlacklist
    let token_blacklist = Arc::new(TokenBlacklist::new());

    // 创建配置管理器（使用默认配置）
    let config_manager = Arc::new(
        coco_server::config::config_manager::ConfigManager::new()
            .expect("Failed to create config manager"),
    );

    AppState::new(
        config_manager,
        db_client,
        token_repository,
        token_service,
        token_blacklist,
    )
}

/// 创建测试用的JWT令牌
fn create_test_jwt_token() -> String {
    use jsonwebtoken::{encode, EncodingKey, Header};

    let claims = UserClaims::new(
        "test-user".to_string(),
        "test-user".to_string(),
        vec!["admin".to_string()],
        "simple".to_string(),
        (chrono::Utc::now().timestamp() + 3600) as usize, // 1小时后过期
        chrono::Utc::now().timestamp() as usize,
    );

    let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "test-secret".to_string());
    let header = Header::default();
    let encoding_key = EncodingKey::from_secret(secret.as_ref());

    encode(&header, &claims, &encoding_key).expect("Failed to create test JWT token")
}

/// 创建测试用的路由器
async fn create_test_router() -> Router {
    let app_state = create_test_app_state().await;

    Router::new()
        .route(
            "/auth/request_access_token",
            post(request_access_token_handler),
        )
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            auth_middleware,
        ))
        .with_state(app_state)
}

#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_request_access_token_success() {
    let app = create_test_router().await;
    let jwt_token = create_test_jwt_token();

    // 创建请求
    let request = Request::builder()
        .method("POST")
        .uri("/auth/request_access_token")
        .header("Authorization", format!("Bearer {}", jwt_token))
        .header("Content-Type", "application/json")
        .body(Body::from(
            json!({
                "name": "test-api-token"
            })
            .to_string(),
        ))
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);

    // 验证响应体
    let body_bytes = hyper::body::to_bytes(response.into_body()).await.unwrap();
    let response_json: CreateTokenResponse = serde_json::from_slice(&body_bytes).unwrap();

    // 验证响应格式
    assert!(!response_json.access_token.is_empty());
    assert_eq!(response_json.expire_in, 31536000); // 365天

    // 验证令牌格式（UUID + 64位随机字符串）
    let token_parts: Vec<&str> = response_json.access_token.split('-').collect();
    assert!(token_parts.len() >= 5); // UUID有4个连字符，加上我们的分隔符
}

#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_request_access_token_without_name() {
    let app = create_test_router().await;
    let jwt_token = create_test_jwt_token();

    // 创建不带name的请求
    let request = Request::builder()
        .method("POST")
        .uri("/auth/request_access_token")
        .header("Authorization", format!("Bearer {}", jwt_token))
        .header("Content-Type", "application/json")
        .body(Body::from(json!({}).to_string()))
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);

    // 验证响应体
    let body_bytes = hyper::body::to_bytes(response.into_body()).await.unwrap();
    let response_json: CreateTokenResponse = serde_json::from_slice(&body_bytes).unwrap();

    // 验证响应格式
    assert!(!response_json.access_token.is_empty());
    assert_eq!(response_json.expire_in, 31536000);
}

#[tokio::test]
async fn test_request_access_token_without_auth() {
    let app = create_test_router().await;

    // 创建没有认证头的请求
    let request = Request::builder()
        .method("POST")
        .uri("/auth/request_access_token")
        .header("Content-Type", "application/json")
        .body(Body::from(
            json!({
                "name": "test-token"
            })
            .to_string(),
        ))
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码（应该是401 Unauthorized）
    assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_request_access_token_invalid_jwt() {
    let app = create_test_router().await;

    // 创建带有无效JWT的请求
    let request = Request::builder()
        .method("POST")
        .uri("/auth/request_access_token")
        .header("Authorization", "Bearer invalid-jwt-token")
        .header("Content-Type", "application/json")
        .body(Body::from(
            json!({
                "name": "test-token"
            })
            .to_string(),
        ))
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码（应该是401 Unauthorized）
    assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_request_access_token_malformed_json() {
    let app = create_test_router().await;
    let jwt_token = create_test_jwt_token();

    // 创建带有格式错误JSON的请求
    let request = Request::builder()
        .method("POST")
        .uri("/auth/request_access_token")
        .header("Authorization", format!("Bearer {}", jwt_token))
        .header("Content-Type", "application/json")
        .body(Body::from("{ invalid json }"))
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码（应该是400 Bad Request）
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);
}

#[test]
fn test_create_token_request_deserialization() {
    // 测试有名称的请求
    let json_with_name = r#"{"name": "my-token"}"#;
    let request: CreateTokenRequest = serde_json::from_str(json_with_name).unwrap();
    assert_eq!(request.name, Some("my-token".to_string()));

    // 测试无名称的请求
    let json_without_name = r#"{}"#;
    let request: CreateTokenRequest = serde_json::from_str(json_without_name).unwrap();
    assert_eq!(request.name, None);

    // 测试null名称的请求
    let json_null_name = r#"{"name": null}"#;
    let request: CreateTokenRequest = serde_json::from_str(json_null_name).unwrap();
    assert_eq!(request.name, None);
}

#[test]
fn test_create_token_response_serialization() {
    let response = CreateTokenResponse {
        access_token: "test-token-123".to_string(),
        expire_in: 31536000,
    };

    let json = serde_json::to_string(&response).unwrap();
    let expected_fields = ["access_token", "expire_in"];

    for field in expected_fields.iter() {
        assert!(
            json.contains(field),
            "Response should contain field: {}",
            field
        );
    }

    // 验证反序列化
    let deserialized: CreateTokenResponse = serde_json::from_str(&json).unwrap();
    assert_eq!(deserialized.access_token, "test-token-123");
    assert_eq!(deserialized.expire_in, 31536000);
}
