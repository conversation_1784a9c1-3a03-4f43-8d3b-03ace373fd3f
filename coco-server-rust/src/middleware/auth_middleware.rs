use crate::app_state::AppState;
use crate::auth::user_claims::{AuthType, UserClaims, UserContext};
use crate::error::error::CocoError;
use crate::services::token_service::TokenService;
use axum::{
    extract::State,
    http::{Request, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use jsonwebtoken::{decode, DecodingKey, Validation};
use serde_json::json;
use std::sync::Arc;
use tracing::{error, info, warn};

/// 改进的身份验证中间件
/// 支持JWT和API令牌认证，并传递用户上下文
pub async fn auth_middleware<B>(
    State(app_state): State<AppState>,
    mut request: Request<B>,
    next: Next<B>,
) -> Result<Response, impl IntoResponse>
where
    B: Send,
{
    info!("Processing authentication for request to {}", request.uri());

    // 检查是否需要跳过身份验证的路径
    let path = request.uri().path().to_string();
    if should_skip_auth(&path) {
        info!("Skipping authentication for path: {}", path);
        let response = next.run(request).await;
        info!("Response from handler for path: {}", path);
        return Ok(response);
    }

    // 从请求头中提取认证信息
    let auth_info = extract_auth_info(request.headers());

    match auth_info {
        Some((token, auth_type)) => {
            // 验证令牌并获取用户上下文
            match validate_token_and_get_context(&token, auth_type, &app_state).await {
                Ok(user_context) => {
                    info!(
                        "Authentication successful for user: {} ({})",
                        user_context.username,
                        mask_token(&token)
                    );

                    // 将用户上下文添加到请求扩展中
                    request.extensions_mut().insert(user_context);

                    Ok(next.run(request).await)
                }
                Err(auth_error) => {
                    warn!("Authentication failed: {}", auth_error);
                    let error_response = create_auth_error_response(&auth_error);
                    Err((StatusCode::UNAUTHORIZED, Json(error_response)))
                }
            }
        }
        None => {
            info!("Authentication failed: missing token");
            let error_response = json!({
                "error": "Missing authentication",
                "message": "缺少认证信息。请在请求头中提供Authorization: Bearer <token>或X-API-TOKEN: <token>。"
            });
            Err((StatusCode::UNAUTHORIZED, Json(error_response)))
        }
    }
}

/// 检查是否应该跳过身份验证的路径
fn should_skip_auth(path: &str) -> bool {
    // 不需要身份验证的路径列表
    const SKIP_AUTH_PATHS: &[&str] = &[
        "/setup/_initialize",
        "/health",
        "/account/login",
        "/sso/login/cloud", // SSO登录端点
    ];

    SKIP_AUTH_PATHS.contains(&path)
}

/// 从请求头中提取认证信息
/// 返回 (token, auth_type) 元组
fn extract_auth_info(headers: &axum::http::HeaderMap) -> Option<(String, AuthType)> {
    // 优先检查 Authorization: Bearer 头
    if let Some(auth_header) = headers.get("Authorization") {
        if let Ok(auth_str) = auth_header.to_str() {
            if auth_str.starts_with("Bearer ") {
                let token = auth_str[7..].to_string(); // 移除"Bearer "前缀
                return Some((token, AuthType::JWT));
            }
        }
    }

    // 检查 X-API-TOKEN 头
    if let Some(api_token) = headers.get("X-API-TOKEN") {
        if let Ok(token_str) = api_token.to_str() {
            return Some((token_str.to_string(), AuthType::ApiToken));
        }
    }

    None
}

/// 验证令牌并获取用户上下文
async fn validate_token_and_get_context(
    token: &str,
    auth_type: AuthType,
    app_state: &AppState,
) -> Result<UserContext, String> {
    match auth_type {
        AuthType::JWT => validate_jwt_token(token, app_state).await,
        AuthType::ApiToken => validate_api_token(token, &app_state.token_service).await,
    }
}

/// 验证JWT令牌并返回用户上下文
async fn validate_jwt_token(token: &str, app_state: &AppState) -> Result<UserContext, String> {
    // 首先检查令牌是否在黑名单中
    if app_state.token_blacklist.is_blacklisted(token).await {
        return Err("JWT令牌已被注销".to_string());
    }

    let secret = get_jwt_secret();
    let validation = Validation::default();

    match decode::<UserClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &validation,
    ) {
        Ok(token_data) => {
            let claims = token_data.claims;

            // 检查令牌是否过期
            if claims.is_expired() {
                return Err("JWT令牌已过期".to_string());
            }

            // 创建用户上下文，包含原始令牌
            let user_context = UserContext::from_jwt_claims(&claims, Some(token.to_string()));
            Ok(user_context)
        }
        Err(e) => {
            error!("JWT验证失败: {}", e);
            Err("无效的JWT令牌".to_string())
        }
    }
}

/// 验证API令牌并返回用户上下文
async fn validate_api_token(
    token: &str,
    token_service: &Arc<TokenService>,
) -> Result<UserContext, String> {
    match token_service.validate_api_token(token).await {
        Ok(access_token) => {
            // 更新令牌最后使用时间
            if let Err(e) = token_service.update_last_used(token).await {
                warn!("Failed to update token last used time: {}", e);
            }

            // 创建用户上下文，包含原始令牌
            let user_context = UserContext::from_api_token(
                access_token.user_id.clone(),
                access_token.user_id.clone(), // 使用user_id作为username
                access_token.roles,
                access_token.provider,
                Some(token.to_string()),
            );

            Ok(user_context)
        }
        Err(e) => {
            error!("API令牌验证失败: {}", e);
            Err("无效的API令牌".to_string())
        }
    }
}

/// 创建认证错误响应
fn create_auth_error_response(error_msg: &str) -> serde_json::Value {
    json!({
        "error": "Invalid authentication token",
        "message": error_msg
    })
}

/// 获取JWT密钥，优先从环境变量获取，否则使用默认值
fn get_jwt_secret() -> String {
    std::env::var("JWT_SECRET")
        .unwrap_or_else(|_| "coco-server-jwt-secret-key-change-in-production".to_string())
}

/// 遮蔽令牌以用于日志记录（只显示前几个字符和后几个字符）
fn mask_token(token: &str) -> String {
    if token.len() <= 4 {
        "*".repeat(token.len())
    } else {
        let prefix = &token[..2];
        let suffix = &token[token.len() - 2..];
        format!("{}***{}", prefix, suffix)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::config_manager::ConfigManager;
    use crate::services::token_service::TokenService;
    use axum::{
        body::Body,
        extract::Extension,
        http::{Request, StatusCode},
        middleware::from_fn_with_state,
        response::Json,
        routing::get,
        Router,
    };
    use std::sync::Arc;
    use tower::ServiceExt;

    // 创建测试用的AppState
    async fn create_test_app_state() -> AppState {
        use crate::database::{DatabaseConfig, SurrealDBClient};
        use crate::repositories::token_repository::TokenRepository;

        let config_manager = Arc::new(ConfigManager::new().unwrap());

        // 注意：这需要运行中的SurrealDB实例
        let db_config = DatabaseConfig::default();
        let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());
        let token_repository = Arc::new(TokenRepository::new(db_client.clone()));
        let token_service = Arc::new(TokenService::new(token_repository.clone()));

        AppState::new(
            config_manager,
            db_client,
            token_repository,
            token_service,
            Arc::new(crate::auth::token_blacklist::TokenBlacklist::new()),
        )
    }

    // 测试处理器，用于验证用户上下文是否正确传递
    async fn test_handler_with_context(
        Extension(user_context): Extension<UserContext>,
    ) -> Json<serde_json::Value> {
        Json(json!({
            "user_id": user_context.user_id,
            "username": user_context.username,
            "auth_type": match user_context.auth_type {
                AuthType::JWT => "JWT",
                AuthType::ApiToken => "ApiToken",
            }
        }))
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_auth_middleware_allows_skip_paths() {
        let app_state = create_test_app_state().await;
        let app = Router::new()
            .route("/setup/_initialize", get(|| async { "ok" }))
            .route("/health", get(|| async { "ok" }))
            .route("/account/login", get(|| async { "ok" }))
            .route("/sso/login/cloud", get(|| async { "ok" }))
            .route_layer(from_fn_with_state(app_state, auth_middleware));

        // 测试所有跳过认证的路径
        let paths = [
            "/setup/_initialize",
            "/health",
            "/account/login",
            "/sso/login/cloud",
        ];

        for path in paths {
            let request = Request::builder().uri(path).body(Body::empty()).unwrap();

            let response = app.clone().oneshot(request).await.unwrap();
            assert_eq!(
                response.status(),
                StatusCode::OK,
                "Path {} should be allowed",
                path
            );
        }
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_auth_middleware_blocks_without_token() {
        let app_state = create_test_app_state().await;
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn_with_state(app_state, auth_middleware));

        let request = Request::builder().uri("/test").body(Body::empty()).unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    #[tokio::test]
    async fn test_extract_auth_info() {
        use axum::http::HeaderMap;

        // 测试Bearer令牌提取
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", "Bearer test-jwt-token".parse().unwrap());

        let result = extract_auth_info(&headers);
        assert!(result.is_some());
        let (token, auth_type) = result.unwrap();
        assert_eq!(token, "test-jwt-token");
        assert_eq!(auth_type, AuthType::JWT);

        // 测试API令牌提取
        let mut headers = HeaderMap::new();
        headers.insert("X-API-TOKEN", "test-api-token".parse().unwrap());

        let result = extract_auth_info(&headers);
        assert!(result.is_some());
        let (token, auth_type) = result.unwrap();
        assert_eq!(token, "test-api-token");
        assert_eq!(auth_type, AuthType::ApiToken);

        // 测试无认证信息
        let headers = HeaderMap::new();
        let result = extract_auth_info(&headers);
        assert!(result.is_none());
    }

    #[tokio::test]
    async fn test_should_skip_auth() {
        assert!(should_skip_auth("/setup/_initialize"));
        assert!(should_skip_auth("/health"));
        assert!(should_skip_auth("/account/login"));
        assert!(should_skip_auth("/sso/login/cloud"));
        assert!(!should_skip_auth("/api/test"));
        assert!(!should_skip_auth("/account/profile"));
        assert!(!should_skip_auth("/auth/request_access_token"));
    }

    #[tokio::test]
    async fn test_mask_token() {
        assert_eq!(mask_token("abc"), "***");
        assert_eq!(mask_token("abcd"), "****");
        assert_eq!(mask_token("abcdef"), "ab***ef");
        assert_eq!(mask_token("test-token-123"), "te***23");
        assert_eq!(mask_token(""), "");
    }

    #[tokio::test]
    async fn test_create_auth_error_response() {
        let response = create_auth_error_response("测试错误消息");
        assert_eq!(response["error"], "Invalid authentication token");
        assert_eq!(response["message"], "测试错误消息");
    }
}
