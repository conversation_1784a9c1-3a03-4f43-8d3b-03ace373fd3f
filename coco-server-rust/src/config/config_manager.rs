use crate::config::models::Config;
use crate::database::DatabaseConfig;
use crate::error::error::CocoError;
use crate::error::result::Result;
use std::env;
use std::fs;
use tracing::{debug, error, info, warn};

#[derive(Clone)]
pub struct ConfigManager {
    config: Config,
}

impl ConfigManager {
    pub fn new() -> Result<Self> {
        // 尝试从环境变量获取配置文件路径，默认为当前目录下的coco.yml
        let config_path = env::var("COCO_CONFIG_PATH").unwrap_or_else(|_| "coco.yml".to_string());

        info!("Loading configuration from: {}", config_path);

        // 读取配置文件
        let config_content = fs::read_to_string(&config_path).map_err(|e| {
            error!("Failed to read config file '{}': {}", config_path, e);
            CocoError::ConfigError(format!(
                "Failed to read config file '{}': {}",
                config_path, e
            ))
        })?;

        // 解析YAML配置
        let config: Config = serde_yaml::from_str(&config_content).map_err(|e| {
            error!("Failed to parse config file '{}': {}", config_path, e);
            CocoError::ConfigError(format!(
                "Failed to parse config file '{}': {}",
                config_path, e
            ))
        })?;

        debug!("Configuration loaded successfully");

        Ok(ConfigManager { config })
    }

    pub fn get_config(&self) -> &Config {
        &self.config
    }

    pub fn get_port(&self) -> u16 {
        // 默认端口为9000（与DeepWiki文档中提到的端口一致）
        let default_port = 9000;

        // 检查环境变量，按优先级排序
        // 1. COCO_PORT (最高优先级)
        if let Ok(port_str) = env::var("COCO_PORT") {
            if let Ok(port) = port_str.parse::<u16>() {
                info!("Using port from COCO_PORT environment variable: {}", port);
                return port;
            } else {
                error!("Invalid COCO_PORT environment variable: {}", port_str);
            }
        }

        // 2. PORT
        if let Ok(port_str) = env::var("PORT") {
            if let Ok(port) = port_str.parse::<u16>() {
                info!("Using port from PORT environment variable: {}", port);
                return port;
            } else {
                error!("Invalid PORT environment variable: {}", port_str);
            }
        }

        // 3. SERVER_PORT
        if let Ok(port_str) = env::var("SERVER_PORT") {
            if let Ok(port) = port_str.parse::<u16>() {
                info!("Using port from SERVER_PORT environment variable: {}", port);
                return port;
            } else {
                error!("Invalid SERVER_PORT environment variable: {}", port_str);
            }
        }

        // 然后检查配置文件中的API_BINDING或WEB_BINDING
        if let Some(env_config) = &self.config.env {
            // 检查API_BINDING (优先级高于WEB_BINDING)
            if let Some(api_binding) = &env_config.api_binding {
                if let Some(port) = Self::extract_port_from_binding(api_binding) {
                    info!("Using port from API_BINDING in config: {}", port);
                    return port;
                }
            }

            // 检查WEB_BINDING
            if let Some(web_binding) = &env_config.web_binding {
                if let Some(port) = Self::extract_port_from_binding(web_binding) {
                    info!("Using port from WEB_BINDING in config: {}", port);
                    return port;
                }
            }
        }

        // 返回默认端口
        info!("Using default port: {}", default_port);
        default_port
    }

    /// 获取Web服务绑定地址
    pub fn get_web_binding(&self) -> Option<String> {
        // 检查环境变量
        if let Ok(web_binding) = env::var("WEB_BINDING") {
            info!(
                "Using WEB_BINDING from environment variable: {}",
                web_binding
            );
            return Some(web_binding);
        }

        // 检查配置文件
        if let Some(env_config) = &self.config.env {
            if let Some(web_binding) = &env_config.web_binding {
                info!("Using WEB_BINDING from config: {}", web_binding);
                return Some(web_binding.clone());
            }
        }

        // 返回默认值
        info!("Using default WEB_BINDING: 0.0.0.0:9000");
        Some("0.0.0.0:9000".to_string())
    }

    /// 获取Web服务端口
    pub fn get_web_port(&self) -> u16 {
        if let Some(web_binding) = self.get_web_binding() {
            if let Some(port) = Self::extract_port_from_binding(&web_binding) {
                return port;
            }
        }
        9000 // 默认端口
    }

    /// 获取API服务绑定地址
    pub fn get_api_binding(&self) -> Option<String> {
        // 检查环境变量
        if let Ok(api_binding) = env::var("API_BINDING") {
            info!(
                "Using API_BINDING from environment variable: {}",
                api_binding
            );
            return Some(api_binding);
        }

        // 检查配置文件
        if let Some(env_config) = &self.config.env {
            if let Some(api_binding) = &env_config.api_binding {
                info!("Using API_BINDING from config: {}", api_binding);
                return Some(api_binding.clone());
            }
        }

        // 返回默认值
        info!("Using default API_BINDING: 0.0.0.0:2900");
        Some("0.0.0.0:2900".to_string())
    }

    fn extract_port_from_binding(binding: &str) -> Option<u16> {
        // 绑定格式通常是 "0.0.0.0:9000" 或 ":9000"
        if let Some(colon_pos) = binding.rfind(':') {
            let port_str = &binding[colon_pos + 1..];
            if let Ok(port) = port_str.parse::<u16>() {
                // 验证端口是否在有效范围内
                if port > 0 && port < 65535 {
                    return Some(port);
                } else {
                    error!("Invalid port number in binding '{}': {}", binding, port);
                }
            } else {
                error!("Invalid port format in binding '{}': {}", binding, port_str);
            }
        } else {
            error!("Invalid binding format: {}", binding);
        }
        None
    }

    /// 验证端口配置的有效性
    pub fn validate_port_config(&self) -> Result<()> {
        // 验证Web服务端口
        if let Some(web_binding) = self.get_web_binding() {
            if let Some(web_port) = Self::extract_port_from_binding(&web_binding) {
                self.validate_single_port(web_port, "Web service")?;
            } else {
                return Err(CocoError::ConfigError(
                    "Invalid Web service binding configuration".to_string(),
                ));
            }
        }

        // 验证API服务端口
        if let Some(api_binding) = self.get_api_binding() {
            if let Some(api_port) = Self::extract_port_from_binding(&api_binding) {
                self.validate_single_port(api_port, "API service")?;
            } else {
                return Err(CocoError::ConfigError(
                    "Invalid API service binding configuration".to_string(),
                ));
            }
        }

        // 检查端口冲突
        if let (Some(web_binding), Some(api_binding)) =
            (self.get_web_binding(), self.get_api_binding())
        {
            if let (Some(web_port), Some(api_port)) = (
                Self::extract_port_from_binding(&web_binding),
                Self::extract_port_from_binding(&api_binding),
            ) {
                if web_port == api_port {
                    return Err(CocoError::ConfigError(
                        format!("Web service port ({}) and API service port ({}) conflict. They must be different.", web_port, api_port)
                    ));
                }
            }
        }

        info!("Port configuration is valid");
        Ok(())
    }

    /// 验证单个端口的有效性
    fn validate_single_port(&self, port: u16, service_name: &str) -> Result<()> {
        // 检查端口是否在有效范围内
        if port == 0 || port >= 65535 {
            return Err(CocoError::ConfigError(format!(
                "Invalid {} port configuration: {}",
                service_name, port
            )));
        }

        // 检查特权端口（1-1023）的使用
        if port < 1024 {
            warn!(
                "Using privileged port for {}: {}. This may require elevated permissions.",
                service_name, port
            );
        }

        info!("{} port configuration is valid: {}", service_name, port);
        Ok(())
    }

    /// 获取SurrealDB配置
    pub fn get_database_config(&self) -> DatabaseConfig {
        // 从配置文件获取基础配置
        let mut db_config = self.config.database.clone().unwrap_or_default();

        // 环境变量覆盖配置文件设置
        if let Some(env_config) = &self.config.env {
            if let Some(url) = &env_config.surrealdb_url {
                db_config.url = url.clone();
            }
            if let Some(namespace) = &env_config.surrealdb_namespace {
                db_config.namespace = namespace.clone();
            }
            if let Some(database) = &env_config.surrealdb_database {
                db_config.database = database.clone();
            }
            if let Some(username) = &env_config.surrealdb_username {
                db_config.username = username.clone();
            }
            if let Some(password) = &env_config.surrealdb_password {
                db_config.password = password.clone();
            }
        }

        // 直接从环境变量获取（最高优先级）
        if let Ok(url) = env::var("SURREALDB_URL") {
            // SurrealDB WebSocket连接不需要协议前缀，直接使用地址
            db_config.url = url;
        }
        if let Ok(namespace) = env::var("SURREALDB_NAMESPACE") {
            db_config.namespace = namespace;
        }
        if let Ok(database) = env::var("SURREALDB_DATABASE") {
            db_config.database = database;
        }
        if let Ok(username) = env::var("SURREALDB_USERNAME") {
            db_config.username = username;
        }
        if let Ok(password) = env::var("SURREALDB_PASSWORD") {
            db_config.password = password;
        }

        info!(
            "SurrealDB配置: URL={}, 命名空间={}, 数据库={}",
            db_config.url, db_config.namespace, db_config.database
        );
        db_config
    }

    /// 验证SurrealDB配置
    pub fn validate_database_config(&self) -> Result<()> {
        let db_config = self.get_database_config();
        db_config.validate().map_err(|e| {
            error!("SurrealDB配置验证失败: {}", e);
            e
        })?;

        info!("SurrealDB配置验证通过");
        Ok(())
    }
}
