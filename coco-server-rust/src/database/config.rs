use crate::error::Result;
use serde::{Deserialize, Serialize};
use surrealdb::{
    engine::remote::ws::{Client, Ws},
    opt::auth::Root,
    Surreal,
};

/// SurrealDB数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库连接URL
    pub url: String,
    /// 命名空间
    pub namespace: String,
    /// 数据库名称
    pub database: String,
    /// 用户名
    pub username: String,
    /// 密码
    pub password: String,
    /// 连接池大小
    pub pool_size: Option<u32>,
    /// 连接超时时间（秒）
    pub timeout: Option<u64>,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "127.0.0.1:8000".to_string(),
            namespace: "coco".to_string(),
            database: "main".to_string(),
            username: "root".to_string(),
            password: "root".to_string(),
            pool_size: Some(10),
            timeout: Some(30),
        }
    }
}

impl DatabaseConfig {
    /// 创建SurrealDB连接
    pub async fn connect(&self) -> Result<Surreal<Client>> {
        tracing::info!("正在连接SurrealDB: {}", self.url);

        // 创建连接 - 使用WebSocket引擎，确保URL格式正确
        let db = Surreal::new::<Ws>(&self.url)
            .await
            .map_err(|e| crate::error::Error::Database(format!("连接SurrealDB失败: {}", e)))?;

        tracing::info!("WebSocket连接建立成功，开始认证...");

        // 认证 - 使用Root用户
        db.signin(Root {
            username: &self.username,
            password: &self.password,
        })
        .await
        .map_err(|e| crate::error::Error::Database(format!("SurrealDB认证失败: {}", e)))?;

        tracing::info!("认证成功，选择命名空间和数据库...");

        // 选择命名空间和数据库
        db.use_ns(&self.namespace)
            .use_db(&self.database)
            .await
            .map_err(|e| crate::error::Error::Database(format!("选择数据库失败: {}", e)))?;

        tracing::info!(
            "SurrealDB连接成功: {} -> 命名空间: {} -> 数据库: {}",
            self.url,
            self.namespace,
            self.database
        );

        Ok(db)
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        if self.url.is_empty() {
            return Err(crate::error::Error::config("数据库URL不能为空"));
        }

        if self.namespace.is_empty() {
            return Err(crate::error::Error::config("命名空间不能为空"));
        }

        if self.database.is_empty() {
            return Err(crate::error::Error::config("数据库名称不能为空"));
        }

        if self.username.is_empty() {
            return Err(crate::error::Error::config("用户名不能为空"));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = DatabaseConfig::default();
        assert_eq!(config.url, "127.0.0.1:8000");
        assert_eq!(config.namespace, "coco");
        assert_eq!(config.database, "main");
    }

    #[test]
    fn test_config_validation() {
        let mut config = DatabaseConfig::default();
        assert!(config.validate().is_ok());

        config.url = "".to_string();
        assert!(config.validate().is_err());
    }
}
