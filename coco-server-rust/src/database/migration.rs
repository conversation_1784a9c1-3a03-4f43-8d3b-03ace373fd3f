use crate::database::SurrealDBClient;
use crate::error::Result;
use std::sync::Arc;
use tracing::{info, warn};

/// 数据库迁移管理器
pub struct MigrationManager {
    client: Arc<SurrealDBClient>,
}

impl MigrationManager {
    /// 创建新的迁移管理器
    pub fn new(client: Arc<SurrealDBClient>) -> Self {
        Self { client }
    }

    /// 执行所有迁移
    pub async fn migrate(&self) -> Result<()> {
        info!("开始执行数据库迁移...");

        // 创建迁移记录表
        self.create_migration_table().await?;

        // 执行各个版本的迁移
        self.migrate_v1_initial_schema().await?;
        self.migrate_v2_search_indexes().await?;
        self.migrate_v3_performance_indexes().await?;
        self.migrate_v4_auth_tables().await?;

        info!("数据库迁移完成");
        Ok(())
    }

    /// 创建迁移记录表
    async fn create_migration_table(&self) -> Result<()> {
        self.client
            .db()
            .query(
                "
            DEFINE TABLE migration SCHEMAFULL;
            DEFINE FIELD version ON TABLE migration TYPE string;
            DEFINE FIELD applied_at ON TABLE migration TYPE datetime DEFAULT time::now();
            DEFINE FIELD description ON TABLE migration TYPE string;
            DEFINE INDEX migration_version_unique ON TABLE migration FIELDS version UNIQUE;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("创建迁移表失败: {}", e)))?;

        Ok(())
    }

    /// 检查迁移是否已执行
    async fn is_migration_applied(&self, version: &str) -> Result<bool> {
        let mut result = self
            .client
            .db()
            .query("SELECT * FROM migration WHERE version = $version")
            .bind(("version", version))
            .await
            .map_err(|e| crate::error::Error::Database(format!("检查迁移状态失败: {}", e)))?;

        let migrations: Vec<serde_json::Value> = result
            .take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析迁移结果失败: {}", e)))?;

        Ok(!migrations.is_empty())
    }

    /// 记录迁移执行
    async fn record_migration(&self, version: &str, description: &str) -> Result<()> {
        self.client
            .db()
            .query("CREATE migration SET version = $version, description = $description")
            .bind(("version", version))
            .bind(("description", description))
            .await
            .map_err(|e| crate::error::Error::Database(format!("记录迁移失败: {}", e)))?;

        Ok(())
    }

    /// V1: 初始数据库结构
    async fn migrate_v1_initial_schema(&self) -> Result<()> {
        let version = "v1_initial_schema";

        if self.is_migration_applied(version).await? {
            info!("迁移 {} 已执行，跳过", version);
            return Ok(());
        }

        info!("执行迁移: {}", version);

        // 创建DataSource表
        self.client
            .db()
            .query(
                "
            DEFINE TABLE datasource SCHEMAFULL;

            DEFINE FIELD id ON TABLE datasource TYPE string;
            DEFINE FIELD name ON TABLE datasource TYPE string;
            DEFINE FIELD description ON TABLE datasource TYPE option<string>;
            DEFINE FIELD connector_config ON TABLE datasource TYPE object;
            DEFINE FIELD created_at ON TABLE datasource TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated_at ON TABLE datasource TYPE datetime DEFAULT time::now();
            DEFINE FIELD deleted_at ON TABLE datasource TYPE option<datetime>;

            DEFINE INDEX datasource_name_unique ON TABLE datasource FIELDS name UNIQUE;
            DEFINE INDEX datasource_created_at ON TABLE datasource FIELDS created_at;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("V1迁移失败: {}", e)))?;

        // 创建Document表
        self.client
            .db()
            .query(
                "
            DEFINE TABLE document SCHEMAFULL;

            DEFINE FIELD id ON TABLE document TYPE string;
            DEFINE FIELD datasource_id ON TABLE document TYPE string;
            DEFINE FIELD title ON TABLE document TYPE string;
            DEFINE FIELD content ON TABLE document TYPE string;
            DEFINE FIELD metadata ON TABLE document TYPE option<object>;
            DEFINE FIELD created_at ON TABLE document TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated_at ON TABLE document TYPE datetime DEFAULT time::now();

            DEFINE INDEX document_datasource_id ON TABLE document FIELDS datasource_id;
            DEFINE INDEX document_created_at ON TABLE document FIELDS created_at;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("V1迁移失败: {}", e)))?;

        self.record_migration(version, "初始数据库结构").await?;
        info!("迁移 {} 执行完成", version);
        Ok(())
    }

    /// V2: 搜索索引
    async fn migrate_v2_search_indexes(&self) -> Result<()> {
        let version = "v2_search_indexes";

        if self.is_migration_applied(version).await? {
            info!("迁移 {} 已执行，跳过", version);
            return Ok(());
        }

        info!("执行迁移: {}", version);

        // 定义搜索分析器
        self.client
            .db()
            .query(
                "
            DEFINE ANALYZER basic_analyzer
                TOKENIZERS blank, punct, camel, class
                FILTERS lowercase, ascii;

            DEFINE ANALYZER chinese_analyzer
                TOKENIZERS blank, punct, class
                FILTERS lowercase, ascii, ngram(2,3);

            DEFINE ANALYZER pinyin_analyzer
                TOKENIZERS blank, punct, class
                FILTERS lowercase, ascii;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("V2迁移失败: {}", e)))?;

        // 定义搜索索引
        self.client
            .db()
            .query(
                "
            DEFINE INDEX datasource_search ON TABLE datasource
                FIELDS name, description
                SEARCH ANALYZER basic_analyzer BM25 HIGHLIGHTS;

            DEFINE INDEX datasource_chinese_search ON TABLE datasource
                FIELDS name, description
                SEARCH ANALYZER chinese_analyzer BM25;

            DEFINE INDEX document_search ON TABLE document
                FIELDS title, content
                SEARCH ANALYZER basic_analyzer BM25 HIGHLIGHTS;

            DEFINE INDEX document_chinese_search ON TABLE document
                FIELDS title, content
                SEARCH ANALYZER chinese_analyzer BM25;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("V2迁移失败: {}", e)))?;

        self.record_migration(version, "搜索索引和分析器").await?;
        info!("迁移 {} 执行完成", version);
        Ok(())
    }

    /// V3: 性能索引
    async fn migrate_v3_performance_indexes(&self) -> Result<()> {
        let version = "v3_performance_indexes";

        if self.is_migration_applied(version).await? {
            info!("迁移 {} 已执行，跳过", version);
            return Ok(());
        }

        info!("执行迁移: {}", version);

        // 添加性能优化索引
        self.client
            .db()
            .query(
                "
            DEFINE INDEX datasource_compound ON TABLE datasource
                FIELDS name, created_at;

            DEFINE INDEX document_compound ON TABLE document
                FIELDS datasource_id, created_at;

            DEFINE INDEX document_title_prefix ON TABLE document
                FIELDS title;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("V3迁移失败: {}", e)))?;

        self.record_migration(version, "性能优化索引").await?;
        info!("迁移 {} 执行完成", version);
        Ok(())
    }

    /// 回滚迁移（谨慎使用）
    pub async fn rollback(&self, target_version: &str) -> Result<()> {
        warn!("开始回滚数据库到版本: {}", target_version);

        // 这里可以实现具体的回滚逻辑
        // 注意：SurrealDB的某些操作（如删除索引）可能不可逆

        warn!("数据库回滚功能需要根据具体需求实现");
        Ok(())
    }

    /// V4: 认证表
    async fn migrate_v4_auth_tables(&self) -> Result<()> {
        let version = "v4_auth_tables";

        if self.is_migration_applied(version).await? {
            info!("迁移 {} 已执行，跳过", version);
            return Ok(());
        }

        info!("执行迁移: {}", version);

        // 创建用户表
        self.client
            .db()
            .query(
                "
            DEFINE TABLE users SCHEMAFULL;

            DEFINE FIELD id ON TABLE users TYPE string;
            DEFINE FIELD username ON TABLE users TYPE string;
            DEFINE FIELD password_hash ON TABLE users TYPE string;
            DEFINE FIELD roles ON TABLE users TYPE array<string>;
            DEFINE FIELD created_at ON TABLE users TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated_at ON TABLE users TYPE datetime DEFAULT time::now();
            DEFINE FIELD last_login ON TABLE users TYPE option<datetime>;
            DEFINE FIELD is_active ON TABLE users TYPE bool DEFAULT true;

            DEFINE INDEX idx_username ON TABLE users COLUMNS username UNIQUE;
            DEFINE INDEX idx_users_created_at ON TABLE users COLUMNS created_at;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("V4迁移失败: {}", e)))?;

        // 创建访问令牌表
        self.client
            .db()
            .query(
                "
            DEFINE TABLE access_tokens SCHEMAFULL;

            DEFINE FIELD id ON TABLE access_tokens TYPE string;
            DEFINE FIELD access_token ON TABLE access_tokens TYPE string;
            DEFINE FIELD user_id ON TABLE access_tokens TYPE string;
            DEFINE FIELD name ON TABLE access_tokens TYPE string;
            DEFINE FIELD provider ON TABLE access_tokens TYPE string;
            DEFINE FIELD token_type ON TABLE access_tokens TYPE string;
            DEFINE FIELD roles ON TABLE access_tokens TYPE array<string>;
            DEFINE FIELD permissions ON TABLE access_tokens TYPE array<string>;
            DEFINE FIELD expire_in ON TABLE access_tokens TYPE int;
            DEFINE FIELD created_at ON TABLE access_tokens TYPE datetime DEFAULT time::now();
            DEFINE FIELD last_used ON TABLE access_tokens TYPE option<datetime>;
            DEFINE FIELD is_active ON TABLE access_tokens TYPE bool DEFAULT true;

            DEFINE INDEX idx_access_token ON TABLE access_tokens COLUMNS access_token UNIQUE;
            DEFINE INDEX idx_user_tokens ON TABLE access_tokens COLUMNS user_id;
            DEFINE INDEX idx_tokens_created_at ON TABLE access_tokens COLUMNS created_at;
            DEFINE INDEX idx_tokens_active ON TABLE access_tokens COLUMNS is_active;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("V4迁移失败: {}", e)))?;

        self.record_migration(version, "认证相关表结构").await?;
        info!("迁移 {} 执行完成", version);
        Ok(())
    }

    /// 获取当前迁移状态
    pub async fn get_migration_status(&self) -> Result<Vec<MigrationRecord>> {
        let mut result = self
            .client
            .db()
            .query("SELECT * FROM migration ORDER BY applied_at")
            .await
            .map_err(|e| crate::error::Error::Database(format!("获取迁移状态失败: {}", e)))?;

        let records: Vec<MigrationRecord> = result
            .take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析迁移记录失败: {}", e)))?;

        Ok(records)
    }
}

/// 迁移记录
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct MigrationRecord {
    pub version: String,
    pub applied_at: chrono::DateTime<chrono::Utc>,
    pub description: String,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::DatabaseConfig;

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_migration() {
        let config = DatabaseConfig::default();
        let client = SurrealDBClient::new(config).await.unwrap();
        let migration_manager = MigrationManager::new(Arc::new(client));

        let result = migration_manager.migrate().await;
        assert!(result.is_ok());

        let status = migration_manager.get_migration_status().await.unwrap();
        assert!(!status.is_empty());
    }
}
