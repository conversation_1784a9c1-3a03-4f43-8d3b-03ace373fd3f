use surrealdb::{
    engine::remote::ws::{Client, Ws},
    opt::auth::Root,
    Surreal,
};
use tracing::{error, info};

/// 调试SurrealDB连接问题
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化详细日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .init();

    println!("🔍 SurrealDB连接调试");
    println!("==================");

    // 连接参数
    let url = "127.0.0.1:8000";
    let username = "root";
    let password = "root";
    let namespace = "coco";
    let database = "main";

    println!("📋 连接参数:");
    println!("   URL: {}", url);
    println!("   用户名: {}", username);
    println!("   密码: {}", password);
    println!("   命名空间: {}", namespace);
    println!("   数据库: {}", database);
    println!();

    // 步骤1: 创建连接
    println!("🔗 步骤1: 创建WebSocket连接...");
    let db = match Surreal::new::<Ws>(url).await {
        Ok(db) => {
            println!("✅ WebSocket连接创建成功!");
            db
        }
        Err(e) => {
            error!("❌ WebSocket连接失败: {}", e);
            println!("💡 请检查:");
            println!("   1. SurrealDB服务是否已启动");
            println!("   2. 端口8000是否可访问");
            println!("   3. 防火墙设置");
            return Err(e.into());
        }
    };

    // 步骤2: 认证
    println!("🔐 步骤2: 进行Root用户认证...");
    match db
        .signin(Root {
            username,
            password,
        })
        .await
    {
        Ok(_) => {
            println!("✅ Root用户认证成功!");
        }
        Err(e) => {
            error!("❌ 认证失败: {}", e);
            println!("💡 请检查:");
            println!("   1. 用户名密码是否正确");
            println!("   2. SurrealDB启动时是否设置了正确的认证参数");
            return Err(e.into());
        }
    }

    // 步骤3: 选择命名空间
    println!("📁 步骤3: 选择命名空间 '{}'...", namespace);
    match db.use_ns(namespace).await {
        Ok(_) => {
            println!("✅ 命名空间选择成功!");
        }
        Err(e) => {
            error!("❌ 选择命名空间失败: {}", e);
            println!("💡 命名空间可能不存在，但这通常不是问题");
            // 不返回错误，继续执行
        }
    }

    // 步骤4: 选择数据库
    println!("🗄️ 步骤4: 选择数据库 '{}'...", database);
    match db.use_db(database).await {
        Ok(_) => {
            println!("✅ 数据库选择成功!");
        }
        Err(e) => {
            error!("❌ 选择数据库失败: {}", e);
            println!("💡 数据库可能不存在，但这通常不是问题");
            // 不返回错误，继续执行
        }
    }

    // 步骤5: 测试基本查询
    println!("📊 步骤5: 测试基本查询...");
    match db.query("SELECT 1 as test").await {
        Ok(response) => {
            println!("✅ 基本查询成功!");
            info!("查询响应: {:?}", response);
        }
        Err(e) => {
            error!("❌ 基本查询失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤6: 测试版本信息
    println!("ℹ️ 步骤6: 获取版本信息...");
    match db.query("SELECT * FROM $version").await {
        Ok(response) => {
            println!("✅ 版本信息查询成功!");
            info!("版本信息: {:?}", response);
        }
        Err(e) => {
            error!("⚠️ 版本信息查询失败: {}", e);
            // 不返回错误，这不是关键功能
        }
    }

    // 步骤7: 测试创建简单表
    println!("📝 步骤7: 测试创建简单表...");
    let create_sql = "CREATE test_debug SET name = 'debug_test', value = 123";
    match db.query(create_sql).await {
        Ok(response) => {
            println!("✅ 创建记录成功!");
            info!("创建响应: {:?}", response);
        }
        Err(e) => {
            error!("❌ 创建记录失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤8: 查询刚创建的记录
    println!("🔍 步骤8: 查询刚创建的记录...");
    match db.query("SELECT * FROM test_debug").await {
        Ok(response) => {
            println!("✅ 查询记录成功!");
            info!("查询结果: {:?}", response);
        }
        Err(e) => {
            error!("❌ 查询记录失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤9: 清理测试数据
    println!("🧹 步骤9: 清理测试数据...");
    match db.query("DELETE test_debug").await {
        Ok(_) => {
            println!("✅ 清理成功!");
        }
        Err(e) => {
            error!("⚠️ 清理失败: {}", e);
            // 不返回错误
        }
    }

    println!();
    println!("🎉 所有连接测试完成!");
    println!("✅ SurrealDB连接工作正常，可以开始使用数据库功能。");

    Ok(())
}
