use surrealdb::{
    engine::remote::http::{Client, Http},
    opt::auth::Root,
    Surreal,
};

/// 使用HTTP协议测试SurrealDB连接
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 SurrealDB HTTP连接测试");
    println!("========================");

    // 连接参数
    let url = "http://127.0.0.1:8000";
    let username = "root";
    let password = "root";
    let namespace = "coco";
    let database = "main";

    println!("📋 连接参数:");
    println!("   URL: {}", url);
    println!("   用户名: {}", username);
    println!("   命名空间: {}", namespace);
    println!("   数据库: {}", database);
    println!();

    // 创建HTTP连接
    println!("🔗 创建HTTP连接...");
    let db: Surreal<Client> = match Surreal::new::<Http>(url).await {
        Ok(db) => {
            println!("✅ HTTP连接创建成功!");
            db
        }
        Err(e) => {
            println!("❌ HTTP连接失败: {}", e);
            return Err(e.into());
        }
    };

    // 认证
    println!("🔐 进行Root用户认证...");
    match db
        .signin(Root {
            username,
            password,
        })
        .await
    {
        Ok(_) => {
            println!("✅ 认证成功!");
        }
        Err(e) => {
            println!("❌ 认证失败: {}", e);
            return Err(e.into());
        }
    }

    // 选择命名空间和数据库
    println!("📁 选择命名空间和数据库...");
    match db.use_ns(namespace).use_db(database).await {
        Ok(_) => {
            println!("✅ 命名空间和数据库选择成功!");
        }
        Err(e) => {
            println!("❌ 选择失败: {}", e);
            return Err(e.into());
        }
    }

    // 测试基本查询
    println!("📊 测试基本查询...");
    match db.query("SELECT 1 as test").await {
        Ok(mut response) => {
            println!("✅ 基本查询成功!");
            let result: Option<i32> = response.take(0)?;
            println!("   查询结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 基本查询失败: {}", e);
            return Err(e.into());
        }
    }

    // 测试创建记录
    println!("📝 测试创建记录...");
    let create_sql = "CREATE test_http SET name = 'http_test', value = 456";
    match db.query(create_sql).await {
        Ok(mut response) => {
            println!("✅ 创建记录成功!");
            let result: Option<serde_json::Value> = response.take(0)?;
            println!("   创建结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 创建记录失败: {}", e);
            return Err(e.into());
        }
    }

    // 查询记录
    println!("🔍 查询刚创建的记录...");
    match db.query("SELECT * FROM test_http").await {
        Ok(mut response) => {
            println!("✅ 查询记录成功!");
            let result: Vec<serde_json::Value> = response.take(0)?;
            println!("   查询结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 查询记录失败: {}", e);
            return Err(e.into());
        }
    }

    // 清理
    println!("🧹 清理测试数据...");
    match db.query("DELETE test_http").await {
        Ok(_) => {
            println!("✅ 清理成功!");
        }
        Err(e) => {
            println!("⚠️ 清理失败: {}", e);
        }
    }

    println!();
    println!("🎉 HTTP连接测试完成!");
    println!("✅ SurrealDB HTTP连接工作正常。");

    Ok(())
}
