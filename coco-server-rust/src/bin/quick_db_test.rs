use coco_server::database::{DatabaseConfig, SurrealDBClient};
use tracing::error;

/// 快速SurrealDB连接测试
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化简单日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .with_target(false)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false)
        .init();

    println!("🔍 快速SurrealDB连接测试");
    println!("========================");

    // 使用默认配置
    let config = DatabaseConfig::default();
    println!(
        "📋 连接信息: {}:{}/{}",
        config.url, config.namespace, config.database
    );

    // 测试连接
    print!("🔗 正在连接数据库... ");
    match SurrealDBClient::new(config.clone()).await {
        Ok(client) => {
            println!("✅ 成功!");

            // 快速健康检查
            print!("🏥 健康检查... ");
            match client.health_check().await {
                Ok(true) => {
                    println!("✅ 通过!");

                    // 简单查询测试
                    print!("📊 查询测试... ");
                    match client.query("SELECT 1 as test").await {
                        Ok(_) => {
                            println!("✅ 成功!");
                            println!("\n🎉 数据库连接正常，可以开始开发!");
                        }
                        Err(e) => {
                            println!("❌ 失败: {}", e);
                            return Err(e.into());
                        }
                    }
                }
                Ok(false) => {
                    println!("⚠️ 健康检查失败");
                    return Err("健康检查失败".into());
                }
                Err(e) => {
                    println!("❌ 错误: {}", e);
                    return Err(e.into());
                }
            }
        }
        Err(e) => {
            println!("❌ 失败!");
            error!("连接错误: {}", e);
            println!("\n💡 请检查:");
            println!("   1. SurrealDB服务是否已启动");
            println!("   2. 连接地址是否正确: {}", config.url);
            println!(
                "   3. 用户名密码是否正确: {}/{}",
                config.username, config.password
            );
            println!("\n🚀 启动命令:");
            println!("   surreal start --user root --pass root rocksdb:/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-server/mydatabase.db");
            return Err(e.into());
        }
    }

    Ok(())
}
