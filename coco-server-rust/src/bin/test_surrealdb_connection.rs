use coco_server::database::{DatabaseConfig, SurrealDBClient};
use serde::{Deserialize, Serialize};
use std::time::Instant;
use tracing::{error, info, warn};

/// 测试数据结构
#[derive(Debug, Serialize, Deserialize, Clone)]
struct TestRecord {
    id: Option<String>,
    name: String,
    value: i32,
    created_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// SurrealDB连接测试主程序
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    info!("🚀 开始SurrealDB连接测试...");

    // 创建数据库配置
    let config = DatabaseConfig::default();
    info!("📋 数据库配置:");
    info!("   URL: {}", config.url);
    info!("   命名空间: {}", config.namespace);
    info!("   数据库: {}", config.database);
    info!("   用户名: {}", config.username);

    // 测试1: 基本连接测试
    info!("\n🔗 测试1: 基本连接测试");
    let start_time = Instant::now();
    
    let client = match SurrealDBClient::new(config.clone()).await {
        Ok(client) => {
            let duration = start_time.elapsed();
            info!("✅ 连接成功! 耗时: {:?}", duration);
            client
        }
        Err(e) => {
            error!("❌ 连接失败: {}", e);
            return Err(e.into());
        }
    };

    // 测试2: 健康检查
    info!("\n🏥 测试2: 健康检查");
    let start_time = Instant::now();
    
    match client.health_check().await {
        Ok(true) => {
            let duration = start_time.elapsed();
            info!("✅ 健康检查通过! 耗时: {:?}", duration);
        }
        Ok(false) => {
            warn!("⚠️ 健康检查失败，但连接正常");
        }
        Err(e) => {
            error!("❌ 健康检查错误: {}", e);
            return Err(e.into());
        }
    }

    // 测试3: 基本查询测试
    info!("\n📊 测试3: 基本查询测试");
    let start_time = Instant::now();
    
    match client.query("SELECT 1 as test_value, time::now() as current_time").await {
        Ok(response) => {
            let duration = start_time.elapsed();
            info!("✅ 基本查询成功! 耗时: {:?}", duration);
            info!("   查询结果: {:?}", response);
        }
        Err(e) => {
            error!("❌ 基本查询失败: {}", e);
            return Err(e.into());
        }
    }

    // 测试4: 表操作测试
    info!("\n📝 测试4: 表操作测试");
    
    // 4.1 创建测试表
    info!("   4.1 创建测试表...");
    let create_table_sql = r#"
        DEFINE TABLE test_connection SCHEMAFULL;
        DEFINE FIELD name ON TABLE test_connection TYPE string;
        DEFINE FIELD value ON TABLE test_connection TYPE int;
        DEFINE FIELD created_at ON TABLE test_connection TYPE datetime DEFAULT time::now();
    "#;
    
    let start_time = Instant::now();
    match client.query(create_table_sql).await {
        Ok(_) => {
            let duration = start_time.elapsed();
            info!("   ✅ 测试表创建成功! 耗时: {:?}", duration);
        }
        Err(e) => {
            error!("   ❌ 测试表创建失败: {}", e);
            return Err(e.into());
        }
    }

    // 4.2 插入测试数据
    info!("   4.2 插入测试数据...");
    let test_record = TestRecord {
        id: None,
        name: "连接测试记录".to_string(),
        value: 42,
        created_at: None,
    };

    let start_time = Instant::now();
    match client.create("test_connection", &test_record).await {
        Ok(created_record) => {
            let duration = start_time.elapsed();
            info!("   ✅ 数据插入成功! 耗时: {:?}", duration);
            info!("   插入的记录: {:?}", created_record);
        }
        Err(e) => {
            error!("   ❌ 数据插入失败: {}", e);
            return Err(e.into());
        }
    }

    // 4.3 查询测试数据
    info!("   4.3 查询测试数据...");
    let start_time = Instant::now();
    
    match client.query("SELECT * FROM test_connection").await {
        Ok(response) => {
            let duration = start_time.elapsed();
            info!("   ✅ 数据查询成功! 耗时: {:?}", duration);
            info!("   查询结果: {:?}", response);
        }
        Err(e) => {
            error!("   ❌ 数据查询失败: {}", e);
            return Err(e.into());
        }
    }

    // 4.4 清理测试数据
    info!("   4.4 清理测试数据...");
    let start_time = Instant::now();
    
    match client.query("DELETE test_connection").await {
        Ok(_) => {
            let duration = start_time.elapsed();
            info!("   ✅ 测试数据清理成功! 耗时: {:?}", duration);
        }
        Err(e) => {
            warn!("   ⚠️ 测试数据清理失败: {}", e);
            // 不返回错误，因为这不是关键操作
        }
    }

    // 测试5: 并发连接测试
    info!("\n🔄 测试5: 并发连接测试");
    let start_time = Instant::now();
    
    let mut handles = vec![];
    for i in 0..5 {
        let config_clone = config.clone();
        let handle = tokio::spawn(async move {
            let client = SurrealDBClient::new(config_clone).await?;
            let result = client.health_check().await?;
            info!("   并发连接 {} 健康检查: {}", i + 1, result);
            Ok::<bool, coco_server::error::Error>(result)
        });
        handles.push(handle);
    }

    let mut success_count = 0;
    for handle in handles {
        match handle.await {
            Ok(Ok(true)) => success_count += 1,
            Ok(Ok(false)) => warn!("   并发连接健康检查失败"),
            Ok(Err(e)) => error!("   并发连接错误: {}", e),
            Err(e) => error!("   并发任务错误: {}", e),
        }
    }

    let duration = start_time.elapsed();
    info!("   ✅ 并发测试完成! 成功连接: {}/5, 耗时: {:?}", success_count, duration);

    // 测试总结
    info!("\n🎉 SurrealDB连接测试完成!");
    info!("📊 测试总结:");
    info!("   ✅ 基本连接: 成功");
    info!("   ✅ 健康检查: 成功");
    info!("   ✅ 基本查询: 成功");
    info!("   ✅ 表操作: 成功");
    info!("   ✅ 并发连接: {}/5 成功", success_count);

    if success_count >= 4 {
        info!("🎊 所有测试通过! SurrealDB连接工作正常。");
    } else {
        warn!("⚠️ 部分测试失败，请检查数据库配置和网络连接。");
    }

    Ok(())
}
