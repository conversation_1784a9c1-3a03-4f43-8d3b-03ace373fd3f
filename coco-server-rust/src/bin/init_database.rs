use surrealdb::{
    engine::remote::ws::{Client, Ws},
    opt::auth::Root,
    Surreal,
};

/// 初始化SurrealDB命名空间和数据库
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 SurrealDB数据库初始化");
    println!("======================");

    // 连接参数
    let url = "127.0.0.1:8000";
    let username = "root";
    let password = "root";
    let namespace = "coco";
    let database = "main";

    println!("📋 初始化参数:");
    println!("   URL: {}", url);
    println!("   用户名: {}", username);
    println!("   命名空间: {}", namespace);
    println!("   数据库: {}", database);
    println!();

    // 步骤1: 连接到SurrealDB
    println!("🔗 步骤1: 连接到SurrealDB...");
    let db = match Surreal::new::<Ws>(url).await {
        Ok(db) => {
            println!("✅ 连接成功!");
            db
        }
        Err(e) => {
            println!("❌ 连接失败: {}", e);
            return Err(e.into());
        }
    };

    // 步骤2: 使用root用户认证
    println!("🔐 步骤2: Root用户认证...");
    match db.signin(Root { username, password }).await {
        Ok(_) => {
            println!("✅ Root认证成功!");
        }
        Err(e) => {
            println!("❌ 认证失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤3: 创建命名空间
    println!("📁 步骤3: 创建命名空间 '{}'...", namespace);
    let create_ns_sql = format!("DEFINE NAMESPACE {}", namespace);
    match db.query(&create_ns_sql).await {
        Ok(_) => {
            println!("✅ 命名空间创建成功!");
        }
        Err(e) => {
            println!("⚠️ 命名空间创建失败 (可能已存在): {}", e);
            // 不返回错误，继续执行
        }
    }

    // 步骤4: 使用命名空间
    println!("📂 步骤4: 使用命名空间...");
    match db.use_ns(namespace).await {
        Ok(_) => {
            println!("✅ 命名空间使用成功!");
        }
        Err(e) => {
            println!("❌ 使用命名空间失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤5: 创建数据库
    println!("🗄️ 步骤5: 创建数据库 '{}'...", database);
    let create_db_sql = format!("DEFINE DATABASE {}", database);
    match db.query(&create_db_sql).await {
        Ok(_) => {
            println!("✅ 数据库创建成功!");
        }
        Err(e) => {
            println!("⚠️ 数据库创建失败 (可能已存在): {}", e);
            // 不返回错误，继续执行
        }
    }

    // 步骤6: 使用数据库
    println!("📊 步骤6: 使用数据库...");
    match db.use_db(database).await {
        Ok(_) => {
            println!("✅ 数据库使用成功!");
        }
        Err(e) => {
            println!("❌ 使用数据库失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤7: 测试基本查询
    println!("📊 步骤7: 测试基本查询...");
    match db.query("INFO FOR DB").await {
        Ok(mut response) => {
            println!("✅ 基本查询成功!");
            let result: Option<serde_json::Value> = response.take(0)?;
            println!("   查询结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 基本查询失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤8: 创建一个测试表
    println!("📝 步骤8: 创建测试表...");
    let create_table_sql = r#"
        DEFINE TABLE test_init SCHEMAFULL;
        DEFINE FIELD name ON TABLE test_init TYPE string;
        DEFINE FIELD value ON TABLE test_init TYPE int;
        DEFINE FIELD created_at ON TABLE test_init TYPE datetime DEFAULT time::now();
    "#;

    match db.query(create_table_sql).await {
        Ok(_) => {
            println!("✅ 测试表创建成功!");
        }
        Err(e) => {
            println!("⚠️ 测试表创建失败: {}", e);
            // 不返回错误，继续执行
        }
    }

    // 步骤9: 插入测试数据
    println!("📥 步骤9: 插入测试数据...");
    let insert_sql = "CREATE test_init SET name = 'initialization_test', value = 999";
    match db.query(insert_sql).await {
        Ok(mut response) => {
            println!("✅ 测试数据插入成功!");
            let result: Option<serde_json::Value> = response.take(0)?;
            println!("   插入结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 测试数据插入失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤10: 查询测试数据
    println!("🔍 步骤10: 查询测试数据...");
    match db.query("SELECT * FROM test_init").await {
        Ok(mut response) => {
            println!("✅ 测试数据查询成功!");
            let result: Vec<serde_json::Value> = response.take(0)?;
            println!("   查询结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 测试数据查询失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤11: 清理测试数据
    println!("🧹 步骤11: 清理测试数据...");
    match db.query("DELETE test_init").await {
        Ok(_) => {
            println!("✅ 测试数据清理成功!");
        }
        Err(e) => {
            println!("⚠️ 测试数据清理失败: {}", e);
        }
    }

    println!();
    println!("🎉 数据库初始化完成!");
    println!(
        "✅ 命名空间 '{}' 和数据库 '{}' 已准备就绪",
        namespace, database
    );
    println!("✅ 现在可以正常使用SurrealDB连接了");

    Ok(())
}
