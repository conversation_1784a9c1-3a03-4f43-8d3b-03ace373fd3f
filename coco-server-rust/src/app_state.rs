use crate::auth::token_blacklist::TokenBlacklist;
use crate::config::config_manager::ConfigManager;
use crate::database::SurrealDBClient;
use crate::repositories::token_repository::TokenRepository;
use crate::services::token_service::TokenService;
use std::sync::Arc;

/// 应用状态结构
#[derive(Clone)]
pub struct AppState {
    pub config_manager: Arc<ConfigManager>,
    pub db_client: Option<Arc<SurrealDBClient>>, // 可选，用于向后兼容
    pub token_repository: Arc<TokenRepository>,
    pub token_service: Arc<TokenService>,
    pub token_blacklist: Arc<TokenBlacklist>,
}

impl AppState {
    /// 创建新的应用状态（向后兼容）
    pub fn new(
        config_manager: Arc<ConfigManager>,
        db_client: Arc<SurrealDBClient>,
        token_repository: Arc<TokenRepository>,
        token_service: Arc<TokenService>,
        token_blacklist: Arc<TokenBlacklist>,
    ) -> Self {
        Self {
            config_manager,
            db_client: Some(db_client),
            token_repository,
            token_service,
            token_blacklist,
        }
    }

    /// 创建使用全局DB连接的应用状态
    pub fn new_with_global_db(
        config_manager: Arc<ConfigManager>,
        token_repository: Arc<TokenRepository>,
        token_service: Arc<TokenService>,
        token_blacklist: Arc<TokenBlacklist>,
    ) -> Self {
        Self {
            config_manager,
            db_client: None,
            token_repository,
            token_service,
            token_blacklist,
        }
    }
}
