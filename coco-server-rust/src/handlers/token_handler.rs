use crate::app_state::AppState;
use crate::auth::user_claims::UserContext;
use crate::handlers::account_handler::ErrorResponse;
use crate::models::access_token::{
    CreateTokenRequest, CreateTokenResponse, RenameTokenRequest, TokenListItem,
};
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use tracing::{error, info};

/// API令牌生成处理器
/// 处理 POST /auth/request_access_token 请求
pub async fn request_access_token_handler(
    State(app_state): State<AppState>,
    Extension(user_context): Extension<UserContext>,
    Json(payload): Json<CreateTokenRequest>,
) -> Result<Json<CreateTokenResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!(
        "Handling POST /auth/request_access_token request for user: {}",
        user_context.username
    );

    // 生成API令牌
    match app_state
        .token_service
        .generate_api_token(&user_context.user_id, payload.name)
        .await
    {
        Ok(token) => {
            info!(
                "API token generated successfully for user: {}",
                user_context.user_id
            );
            Ok(Json(CreateTokenResponse {
                access_token: token.access_token,
                expire_in: token.expire_in,
            }))
        }
        Err(e) => {
            error!("Failed to generate API token: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to generate access token".to_string(),
                    message: Some(format!("Token generation failed: {}", e)),
                }),
            ))
        }
    }
}

/// 获取API令牌列表处理器
/// 处理 GET /auth/access_token/_cat 请求
pub async fn list_access_tokens_handler(
    State(app_state): State<AppState>,
    Extension(user_context): Extension<UserContext>,
) -> Result<Json<Vec<TokenListItem>>, (StatusCode, Json<ErrorResponse>)> {
    info!(
        "Handling GET /auth/access_token/_cat request for user: {}",
        user_context.username
    );

    // 获取用户的所有令牌
    match app_state
        .token_service
        .get_user_tokens(&user_context.user_id)
        .await
    {
        Ok(tokens) => {
            info!(
                "Retrieved {} tokens for user: {}",
                tokens.len(),
                user_context.user_id
            );
            let token_list: Vec<TokenListItem> = tokens.into_iter().map(|t| t.into()).collect();
            Ok(Json(token_list))
        }
        Err(e) => {
            error!("Failed to get user tokens: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get access tokens".to_string(),
                    message: Some(format!("Token retrieval failed: {}", e)),
                }),
            ))
        }
    }
}

/// 删除API令牌处理器
/// 处理 DELETE /auth/access_token/{token_id} 请求
pub async fn delete_access_token_handler(
    State(app_state): State<AppState>,
    Extension(user_context): Extension<UserContext>,
    Path(token_id): Path<String>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!(
        "Handling DELETE /auth/access_token/{} request for user: {}",
        token_id, user_context.username
    );

    // 撤销令牌
    match app_state
        .token_service
        .revoke_token(&token_id, &user_context.user_id)
        .await
    {
        Ok(_) => {
            info!("Token deleted successfully: {}", token_id);
            Ok(Json(serde_json::json!({
                "result": "deleted",
                "id": token_id
            })))
        }
        Err(e) => {
            error!("Failed to delete token: {}", e);
            Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "Token not found or permission denied".to_string(),
                    message: Some(format!("Token deletion failed: {}", e)),
                }),
            ))
        }
    }
}

/// 重命名API令牌处理器
/// 处理 POST /auth/access_token/{token_id}/_rename 请求
pub async fn rename_access_token_handler(
    State(app_state): State<AppState>,
    Extension(user_context): Extension<UserContext>,
    Path(token_id): Path<String>,
    Json(payload): Json<RenameTokenRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!(
        "Handling POST /auth/access_token/{}/_rename request for user: {}",
        token_id, user_context.username
    );

    // 验证请求参数
    if payload.name.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "name is required".to_string(),
                message: Some("Token name cannot be empty".to_string()),
            }),
        ));
    }

    // 重命名令牌
    match app_state
        .token_service
        .rename_token(&token_id, &user_context.user_id, payload.name)
        .await
    {
        Ok(_) => {
            info!("Token renamed successfully: {}", token_id);
            Ok(Json(serde_json::json!({
                "result": "updated",
                "id": token_id
            })))
        }
        Err(e) => {
            error!("Failed to rename token: {}", e);
            Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "Token not found or permission denied".to_string(),
                    message: Some(format!("Token rename failed: {}", e)),
                }),
            ))
        }
    }
}

/// 获取令牌统计信息处理器
/// 处理 GET /auth/access_token/_stats 请求
pub async fn get_token_stats_handler(
    State(app_state): State<AppState>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling GET /auth/access_token/_stats request");

    match app_state.token_service.get_token_stats().await {
        Ok(stats) => Ok(Json(serde_json::json!(stats))),
        Err(e) => {
            error!("Failed to get token stats: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get token statistics".to_string(),
                    message: Some("Database error occurred".to_string()),
                }),
            ))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::auth::user_claims::{AuthType, UserClaims};

    #[tokio::test]
    async fn test_create_token_request_serialization() {
        let request = CreateTokenRequest {
            name: Some("test-token".to_string()),
        };

        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("test-token"));

        let deserialized: CreateTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, Some("test-token".to_string()));
    }

    #[tokio::test]
    async fn test_create_token_response_serialization() {
        let response = CreateTokenResponse {
            access_token: "test-token-123".to_string(),
            expire_in: 31536000,
        };

        let json = serde_json::to_string(&response).unwrap();
        assert!(json.contains("test-token-123"));
        assert!(json.contains("31536000"));

        let deserialized: CreateTokenResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.access_token, "test-token-123");
        assert_eq!(deserialized.expire_in, 31536000);
    }

    #[tokio::test]
    async fn test_rename_token_request_serialization() {
        let request = RenameTokenRequest {
            name: "new-name".to_string(),
        };

        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("new-name"));

        let deserialized: RenameTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, "new-name");
    }

    #[tokio::test]
    async fn test_user_context_extraction() {
        // 测试UserContext的创建和字段访问
        let user_context = UserContext {
            user_id: "test-user".to_string(),
            username: "test-user".to_string(),
            roles: vec!["admin".to_string()],
            auth_type: AuthType::JWT,
            provider: "simple".to_string(),
            original_token: Some("test-token".to_string()),
        };

        assert_eq!(user_context.user_id, "test-user");
        assert_eq!(user_context.username, "test-user");
        assert_eq!(user_context.roles, vec!["admin".to_string()]);
        assert!(user_context.is_admin());
        assert!(user_context.has_role("admin"));
        assert!(!user_context.has_role("user"));
    }

    #[tokio::test]
    async fn test_user_context_from_jwt_claims() {
        let claims = UserClaims::new(
            "test-user".to_string(),
            "test-user".to_string(),
            vec!["admin".to_string()],
            "simple".to_string(),
            (chrono::Utc::now().timestamp() + 3600) as usize, // 1小时后过期
            chrono::Utc::now().timestamp() as usize,
        );

        let user_context = UserContext::from_jwt_claims(&claims, Some("test-token".to_string()));

        assert_eq!(user_context.user_id, "test-user");
        assert_eq!(user_context.username, "test-user");
        assert_eq!(user_context.auth_type, AuthType::JWT);
        assert_eq!(user_context.original_token, Some("test-token".to_string()));
    }

    #[tokio::test]
    async fn test_user_context_from_api_token() {
        let user_context = UserContext::from_api_token(
            "test-user".to_string(),
            "test-user".to_string(),
            vec!["admin".to_string()],
            "access_token".to_string(),
            Some("api-token-123".to_string()),
        );

        assert_eq!(user_context.user_id, "test-user");
        assert_eq!(user_context.username, "test-user");
        assert_eq!(user_context.auth_type, AuthType::ApiToken);
        assert_eq!(
            user_context.original_token,
            Some("api-token-123".to_string())
        );
    }

    #[tokio::test]
    async fn test_request_validation() {
        // 测试空名称的请求
        let request = CreateTokenRequest { name: None };
        let json = serde_json::to_string(&request).unwrap();
        let deserialized: CreateTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, None);

        // 测试有名称的请求
        let request = CreateTokenRequest {
            name: Some("my-token".to_string()),
        };
        let json = serde_json::to_string(&request).unwrap();
        let deserialized: CreateTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, Some("my-token".to_string()));
    }

    #[tokio::test]
    async fn test_error_response_serialization() {
        let error_response = ErrorResponse {
            error: "Invalid request".to_string(),
            message: Some("Request validation failed".to_string()),
        };

        let json = serde_json::to_string(&error_response).unwrap();
        assert!(json.contains("Invalid request"));
        assert!(json.contains("Request validation failed"));

        let deserialized: ErrorResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.error, "Invalid request");
        assert_eq!(
            deserialized.message,
            Some("Request validation failed".to_string())
        );
    }
}
