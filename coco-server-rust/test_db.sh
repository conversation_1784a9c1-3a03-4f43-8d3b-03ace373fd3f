#!/bin/bash

# SurrealDB连接测试脚本
# 使用方法: ./test_db.sh [quick|full]

set -e

echo "🔍 SurrealDB连接测试脚本"
echo "========================"

# 检查SurrealDB是否运行
check_surrealdb() {
    echo "🔍 检查SurrealDB服务状态..."
    
    # 检查端口8000是否被占用
    if lsof -i :8000 >/dev/null 2>&1; then
        echo "✅ SurrealDB服务正在运行 (端口8000已占用)"
        return 0
    else
        echo "❌ SurrealDB服务未运行"
        echo ""
        echo "💡 请先启动SurrealDB服务:"
        echo "   surreal start --user root --pass root rocksdb:/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-server/mydatabase.db"
        echo ""
        return 1
    fi
}

# 运行快速测试
run_quick_test() {
    echo "🚀 运行快速连接测试..."
    echo ""
    cargo run --bin quick_db_test
}

# 运行完整测试
run_full_test() {
    echo "🚀 运行完整连接测试..."
    echo ""
    cargo run --bin test_surrealdb_connection
}

# 主程序
main() {
    local test_type=${1:-quick}
    
    # 检查SurrealDB服务
    if ! check_surrealdb; then
        exit 1
    fi
    
    echo ""
    
    case $test_type in
        "quick")
            run_quick_test
            ;;
        "full")
            run_full_test
            ;;
        *)
            echo "❌ 未知的测试类型: $test_type"
            echo "使用方法: $0 [quick|full]"
            echo "  quick - 快速连接测试 (默认)"
            echo "  full  - 完整功能测试"
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"
