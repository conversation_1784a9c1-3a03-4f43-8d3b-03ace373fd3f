# SurrealDB连接测试文档

## 概述

本文档描述了如何使用SurrealDB连接测试工具来验证数据库连接是否正常工作。

## 前置条件

1. **SurrealDB服务已启动**
   ```bash
   surreal start --user root --pass root rocksdb:/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-server/mydatabase.db
   ```

2. **Rust环境已配置**
   - 确保项目可以正常编译

## 测试工具

### 1. 快速连接测试 (`quick_db_test`)

**用途**: 快速验证数据库连接是否正常

**运行方式**:
```bash
# 方式1: 直接运行
cargo run --bin quick_db_test

# 方式2: 使用测试脚本
./test_db.sh quick
```

**测试内容**:
- 基本连接测试
- 健康检查
- 简单查询测试

**输出示例**:
```
🔍 快速SurrealDB连接测试
========================
📋 连接信息: 127.0.0.1:8000:coco/main
🔗 正在连接数据库... ✅ 成功!
🏥 健康检查... ✅ 通过!
📊 查询测试... ✅ 成功!

🎉 数据库连接正常，可以开始开发!
```

### 2. 完整功能测试 (`test_surrealdb_connection`)

**用途**: 全面测试数据库功能和性能

**运行方式**:
```bash
# 方式1: 直接运行
cargo run --bin test_surrealdb_connection

# 方式2: 使用测试脚本
./test_db.sh full
```

**测试内容**:
1. **基本连接测试** - 验证连接建立
2. **健康检查** - 验证数据库状态
3. **基本查询测试** - 验证SQL查询功能
4. **表操作测试** - 验证CRUD操作
   - 创建测试表
   - 插入测试数据
   - 查询测试数据
   - 清理测试数据
5. **并发连接测试** - 验证多连接处理能力

**输出示例**:
```
🚀 开始SurrealDB连接测试...
📋 数据库配置:
   URL: 127.0.0.1:8000
   命名空间: coco
   数据库: main
   用户名: root

🔗 测试1: 基本连接测试
✅ 连接成功! 耗时: 45.2ms

🏥 测试2: 健康检查
✅ 健康检查通过! 耗时: 12.3ms

📊 测试3: 基本查询测试
✅ 基本查询成功! 耗时: 8.7ms

📝 测试4: 表操作测试
   4.1 创建测试表...
   ✅ 测试表创建成功! 耗时: 15.2ms
   4.2 插入测试数据...
   ✅ 数据插入成功! 耗时: 23.1ms
   4.3 查询测试数据...
   ✅ 数据查询成功! 耗时: 11.4ms
   4.4 清理测试数据...
   ✅ 测试数据清理成功! 耗时: 9.8ms

🔄 测试5: 并发连接测试
   ✅ 并发测试完成! 成功连接: 5/5, 耗时: 156.7ms

🎉 SurrealDB连接测试完成!
📊 测试总结:
   ✅ 基本连接: 成功
   ✅ 健康检查: 成功
   ✅ 基本查询: 成功
   ✅ 表操作: 成功
   ✅ 并发连接: 5/5 成功

🎊 所有测试通过! SurrealDB连接工作正常。
```

## 测试脚本 (`test_db.sh`)

**用途**: 统一的测试入口，自动检查SurrealDB服务状态

**使用方法**:
```bash
# 快速测试 (默认)
./test_db.sh
./test_db.sh quick

# 完整测试
./test_db.sh full
```

**功能**:
- 自动检查SurrealDB服务是否运行
- 提供友好的错误提示
- 统一的测试入口

## 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ 连接失败!
   连接错误: 连接SurrealDB失败: ...
   ```
   
   **解决方案**:
   - 检查SurrealDB服务是否已启动
   - 确认连接地址和端口正确
   - 检查防火墙设置

2. **认证失败**
   ```
   SurrealDB认证失败: ...
   ```
   
   **解决方案**:
   - 确认用户名密码正确 (默认: root/root)
   - 检查SurrealDB启动参数

3. **健康检查失败**
   ```
   ⚠️ 健康检查失败，但连接正常
   ```
   
   **解决方案**:
   - 通常不影响正常使用
   - 检查数据库权限设置

### 调试模式

如需更详细的日志信息，可以设置环境变量：

```bash
RUST_LOG=debug cargo run --bin test_surrealdb_connection
```

## 配置说明

默认数据库配置 (在 `src/database/config.rs` 中定义):

```rust
DatabaseConfig {
    url: "127.0.0.1:8000",
    namespace: "coco",
    database: "main", 
    username: "root",
    password: "root",
    pool_size: Some(10),
    timeout: Some(30),
}
```

如需修改配置，可以：
1. 修改 `DatabaseConfig::default()` 方法
2. 或在代码中创建自定义配置

## 性能基准

基于测试结果的性能参考值：

- **连接建立**: < 100ms
- **健康检查**: < 50ms  
- **简单查询**: < 20ms
- **表创建**: < 50ms
- **数据插入**: < 50ms
- **数据查询**: < 30ms
- **并发连接**: 5个连接 < 200ms

## 下一步

连接测试通过后，您可以：

1. 开始使用 `SurrealDBClient` 进行开发
2. 运行项目的集成测试
3. 启动完整的服务器应用

```bash
# 运行服务器
cargo run

# 运行集成测试  
cargo test
```
