mock/
/logs/
npm-debug.log
yarn-error.log
node_modules
package-lock.json
yarn.lock
coverage/
/web/LICENSE
/web/.temp/
.idea/
run/
.DS_Store
*.sw*
*.un~

app/public
config/manifest.json
app/web/.temp
.umi
docker/.node_modules/*
.vscode/
.umi*

/data
appveyor.yml
.travis.yml
/.public
/vendor
/bin
*.exe
web/.github/
web/report*
log/
generated_*.go
/assets/static.go

# SonarQube scanner
.scannerwork/

docs/.hugo_build.lock
/plugins/enterprise/
coco-server-rust/target
*.log
mydatabase.db
.augment
